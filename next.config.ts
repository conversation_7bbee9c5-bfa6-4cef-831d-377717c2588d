import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        hostname: 'p3-sign.douyinpic.com',
        pathname: '/**',
        port: '',
        protocol: 'https',
      },
      {
        hostname: 'p1-sign.douyinpic.com',
        pathname: '/**',
        port: '',
        protocol: 'https',
      },
      {
        hostname: 'p6-sign.douyinpic.com',
        pathname: '/**',
        port: '',
        protocol: 'https',
      },
      {
        hostname: 'p9-sign.douyinpic.com',
        pathname: '/**',
        port: '',
        protocol: 'https',
      },
      {
        hostname: 'p26-sign.douyinpic.com',
        pathname: '/**',
        port: '',
        protocol: 'https',
      },
      // 添加其他可能的抖音图片域名
      {
        hostname: '*.douyinpic.com',
        pathname: '/**',
        port: '',
        protocol: 'https',
      },
      // 添加其他常见的视频平台图片域名
      {
        hostname: '*.bytedance.com',
        pathname: '/**',
        port: '',
        protocol: 'https',
      },
      {
        hostname: '*.tiktokcdn.com',
        pathname: '/**',
        port: '',
        protocol: 'https',
      },
    ],
  },
}

export default nextConfig
