{"dependencies": {"@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.518.0", "next": "15.3.4", "postcss": "^8.5.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@biomejs/biome": "2.0.0", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.10", "@types/node": "^24.0.3", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "typescript": "^5"}, "name": "tiffany", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbopack", "lint": "next lint", "start": "next start"}, "version": "0.1.0"}