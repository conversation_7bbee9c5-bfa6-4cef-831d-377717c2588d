{"compilerOptions": {"allowJs": true, "esModuleInterop": true, "incremental": true, "isolatedModules": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "moduleResolution": "bundler", "noEmit": true, "paths": {"@/*": ["./app/*"]}, "plugins": [{"name": "next"}], "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "ES2017"}, "exclude": ["node_modules"], "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "./components/**/*.tsx"]}