{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/components/VideoDownload.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useEffect, useState } from 'react'\n\n// 扩展 Window 接口以包含 File System Access API\ndeclare global {\n  interface Window {\n    showDirectoryPicker?: () => Promise<FileSystemDirectoryHandle>\n  }\n}\n\nexport default function VideoDownload() {\n  const [url, setUrl] = useState('')\n  const [status, setStatus] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [selectedFolder, setSelectedFolder] = useState<FileSystemDirectoryHandle | null>(null)\n  const [folderName, setFolderName] = useState('')\n  const [isMounted, setIsMounted] = useState(false)\n\n  useEffect(() => {\n    setIsMounted(true)\n  }, [])\n\n  // 检查浏览器是否支持 File System Access API\n  const isFileSystemAccessSupported = () => {\n    return isMounted && typeof window !== 'undefined' && 'showDirectoryPicker' in window\n  }\n\n  // 选择文件夹\n  const selectFolder = async () => {\n    try {\n      if (!isFileSystemAccessSupported()) {\n        setStatus('您的浏览器不支持文件夹选择功能，将使用默认下载方式')\n        return\n      }\n\n      const dirHandle = await window?.showDirectoryPicker?.()\n      if (dirHandle) {\n        setSelectedFolder(dirHandle)\n        setFolderName(dirHandle.name)\n        setStatus(`已选择文件夹: ${dirHandle.name}`)\n      }\n    } catch (error) {\n      if (error instanceof Error && error.name !== 'AbortError') {\n        console.error('选择文件夹失败:', error)\n        setStatus('选择文件夹失败')\n      }\n    }\n  }\n\n  // 默认下载方法（创建下载链接）\n  const downloadWithDefaultMethod = (blob: Blob, filename: string) => {\n    if (typeof window === 'undefined') return\n\n    const downloadUrl = window.URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = downloadUrl\n    a.download = filename\n    document.body.appendChild(a)\n    a.click()\n    document.body.removeChild(a)\n    window.URL.revokeObjectURL(downloadUrl)\n  }\n\n  const downloadVideo = async () => {\n    if (!url.trim()) {\n      setStatus('请输入视频地址')\n      return\n    }\n\n    setIsLoading(true)\n    setStatus('正在下载视频...')\n\n    try {\n      console.log('开始请求视频:', url.trim())\n\n      const response = await fetch('/api/video', {\n        body: JSON.stringify({ url: url.trim() }),\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        method: 'POST',\n      })\n\n      console.log('API响应状态:', response.status, response.statusText)\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        console.error('API错误响应:', errorData)\n        throw new Error(errorData.details || errorData.error || '下载失败')\n      }\n\n      // 获取文件名\n      const contentDisposition = response.headers.get('content-disposition')\n      let filename = 'video'\n      if (contentDisposition) {\n        const match = contentDisposition.match(/filename=\"?([^\"]+)\"?/)\n        if (match) filename = match[1]\n      } else {\n        // 从URL中提取文件名\n        try {\n          const urlObj = new URL(url)\n          const pathname = urlObj.pathname\n          const lastSlash = pathname.lastIndexOf('/')\n          if (lastSlash !== -1) {\n            filename = pathname.substring(lastSlash + 1) || 'video'\n          }\n        } catch {\n          filename = 'video'\n        }\n      }\n\n      // 确保文件名有扩展名\n      if (!filename.includes('.')) {\n        const contentType = response.headers.get('content-type')\n        if (contentType && contentType.startsWith('video/')) {\n          const ext = contentType.split('/')[1]\n          filename += '.' + ext\n        } else {\n          filename += '.mp4'\n        }\n      }\n\n      // 创建blob并下载\n      const blob = await response.blob()\n\n      // 如果选择了文件夹且浏览器支持，则保存到指定文件夹\n      if (selectedFolder && isFileSystemAccessSupported()) {\n        try {\n          const fileHandle = await selectedFolder.getFileHandle(filename, {\n            create: true,\n          })\n          const writable = await fileHandle.createWritable()\n          await writable.write(blob)\n          await writable.close()\n          setStatus(`视频已保存到文件夹 \"${folderName}\" 中！`)\n        } catch (error) {\n          console.error('保存到文件夹失败:', error)\n          // 如果保存到文件夹失败，回退到默认下载方式\n          downloadWithDefaultMethod(blob, filename)\n          setStatus('保存到指定文件夹失败，已使用默认下载方式')\n        }\n      } else {\n        // 使用默认下载方式\n        downloadWithDefaultMethod(blob, filename)\n        setStatus('视频下载成功！')\n      }\n    } catch (error) {\n      console.error('下载错误:', error)\n\n      // 详细的错误信息\n      let errorMessage = '未知错误'\n      if (error instanceof Error) {\n        errorMessage = error.message\n        if (error.name === 'TypeError' && error.message.includes('fetch')) {\n          errorMessage = '网络连接失败，请检查网络连接或URL是否正确'\n        } else if (error.message.includes('Failed to fetch')) {\n          errorMessage = '无法连接到服务器，请检查网络连接'\n        }\n      }\n\n      setStatus(`下载失败: ${errorMessage}`)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <div className='max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg'>\n      <h1 className='text-3xl font-bold text-gray-800 mb-6 text-center'>视频下载器</h1>\n      <p className='text-gray-600 mb-6 text-center'>\n        输入视频URL地址，点击下载按钮即可下载视频到本地\n      </p>\n\n      <div className='space-y-4'>\n        <div>\n          <label className='block text-sm font-medium text-gray-700 mb-2' htmlFor='videoUrl'>\n            视频地址\n          </label>\n          <textarea\n            className='w-full h-24 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none'\n            id='videoUrl'\n            onChange={(e) => setUrl(e.target.value)}\n            placeholder='请输入视频地址，例如：https://example.com/video.mp4'\n            value={url}\n          />\n        </div>\n\n        {/* 文件夹选择区域 */}\n        <div className='space-y-2'>\n          <label className='block text-sm font-medium text-gray-700'>\n            下载位置{' '}\n            {!isFileSystemAccessSupported() && (\n              <span className='text-gray-500'>(浏览器不支持文件夹选择)</span>\n            )}\n          </label>\n          <div className='flex gap-2'>\n            <button\n              className={`flex-1 py-2 px-4 rounded-md font-medium transition-colors ${\n                !isFileSystemAccessSupported() || isLoading\n                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500'\n              }`}\n              disabled={!isFileSystemAccessSupported() || isLoading}\n              onClick={selectFolder}\n            >\n              {folderName ? `已选择: ${folderName}` : '选择下载文件夹'}\n            </button>\n            {folderName && (\n              <button\n                className='px-3 py-2 text-gray-500 hover:text-gray-700 focus:outline-none'\n                disabled={isLoading}\n                onClick={() => {\n                  setSelectedFolder(null)\n                  setFolderName('')\n                  setStatus('')\n                }}\n                title='清除选择'\n              >\n                ✕\n              </button>\n            )}\n          </div>\n          {!isFileSystemAccessSupported() && (\n            <p className='text-xs text-gray-500'>\n              您的浏览器不支持文件夹选择功能，视频将下载到默认下载文件夹\n            </p>\n          )}\n        </div>\n\n        <button\n          className={`w-full py-3 px-4 rounded-md font-medium text-white transition-colors ${\n            isLoading\n              ? 'bg-gray-400 cursor-not-allowed'\n              : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'\n          }`}\n          disabled={isLoading}\n          onClick={downloadVideo}\n        >\n          {isLoading ? '下载中...' : '下载视频'}\n        </button>\n\n        {status && (\n          <div\n            className={`p-3 rounded-md text-sm ${\n              status.includes('成功')\n                ? 'bg-green-100 text-green-800 border border-green-200'\n                : status.includes('失败') || status.includes('错误')\n                  ? 'bg-red-100 text-red-800 border border-red-200'\n                  : 'bg-blue-100 text-blue-800 border border-blue-200'\n            }`}\n          >\n            {status}\n          </div>\n        )}\n      </div>\n\n      <div className='mt-8 text-sm text-gray-500'>\n        <h3 className='font-medium mb-2'>使用说明：</h3>\n        <ul className='list-disc list-inside space-y-1'>\n          <li>支持大部分视频网站的直链地址</li>\n          <li>URL必须是http或https协议</li>\n          <li>下载超时时间为30秒</li>\n          <li>支持自动识别文件名和格式</li>\n          <li>可选择指定文件夹下载（需要现代浏览器支持）</li>\n          <li>如果不选择文件夹，将使用浏览器默认下载位置</li>\n        </ul>\n\n        <div className='mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md'>\n          <h4 className='font-medium text-blue-800 mb-2'>测试URL示例：</h4>\n          <div className='space-y-2'>\n            <button\n              className='block text-blue-600 hover:text-blue-800 underline text-xs'\n              onClick={() => setUrl('https://www.w3schools.com/html/mov_bbb.mp4')}\n            >\n              点击填入测试视频URL (W3Schools)\n            </button>\n            <button\n              className='block text-blue-600 hover:text-blue-800 underline text-xs'\n              onClick={() =>\n                setUrl('https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4')\n              }\n            >\n              点击填入测试视频URL (Sample Videos)\n            </button>\n          </div>\n        </div>\n\n        <div className='mt-4'>\n          <Link className='text-blue-500 hover:underline text-sm' href='/test'>\n            前往API测试页面\n          </Link>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYe,SAAS;IACtB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;IACvF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,8BAA8B;QAClC,OAAO,aAAa,gBAAkB,eAAe,yBAAyB;IAChF;IAEA,QAAQ;IACR,MAAM,eAAe;QACnB,IAAI;YACF,IAAI,CAAC,+BAA+B;gBAClC,UAAU;gBACV;YACF;;YAEA,MAAM;QAMR,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;gBACzD,QAAQ,KAAK,CAAC,YAAY;gBAC1B,UAAU;YACZ;QACF;IACF;IAEA,iBAAiB;IACjB,MAAM,4BAA4B,CAAC,MAAY;QAC7C,wCAAmC;;QAEnC,MAAM;QACN,MAAM;IAOR;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,IAAI,IAAI,IAAI;YACf,UAAU;YACV;QACF;QAEA,aAAa;QACb,UAAU;QAEV,IAAI;YACF,QAAQ,GAAG,CAAC,WAAW,IAAI,IAAI;YAE/B,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,MAAM,KAAK,SAAS,CAAC;oBAAE,KAAK,IAAI,IAAI;gBAAG;gBACvC,SAAS;oBACP,gBAAgB;gBAClB;gBACA,QAAQ;YACV;YAEA,QAAQ,GAAG,CAAC,YAAY,SAAS,MAAM,EAAE,SAAS,UAAU;YAE5D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,YAAY;gBAC1B,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI;YAC1D;YAEA,QAAQ;YACR,MAAM,qBAAqB,SAAS,OAAO,CAAC,GAAG,CAAC;YAChD,IAAI,WAAW;YACf,IAAI,oBAAoB;gBACtB,MAAM,QAAQ,mBAAmB,KAAK,CAAC;gBACvC,IAAI,OAAO,WAAW,KAAK,CAAC,EAAE;YAChC,OAAO;gBACL,aAAa;gBACb,IAAI;oBACF,MAAM,SAAS,IAAI,IAAI;oBACvB,MAAM,WAAW,OAAO,QAAQ;oBAChC,MAAM,YAAY,SAAS,WAAW,CAAC;oBACvC,IAAI,cAAc,CAAC,GAAG;wBACpB,WAAW,SAAS,SAAS,CAAC,YAAY,MAAM;oBAClD;gBACF,EAAE,OAAM;oBACN,WAAW;gBACb;YACF;YAEA,YAAY;YACZ,IAAI,CAAC,SAAS,QAAQ,CAAC,MAAM;gBAC3B,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;gBACzC,IAAI,eAAe,YAAY,UAAU,CAAC,WAAW;oBACnD,MAAM,MAAM,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE;oBACrC,YAAY,MAAM;gBACpB,OAAO;oBACL,YAAY;gBACd;YACF;YAEA,YAAY;YACZ,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,2BAA2B;YAC3B,IAAI,kBAAkB,+BAA+B;;YAerD,OAAO;gBACL,WAAW;gBACX,0BAA0B,MAAM;gBAChC,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YAEvB,UAAU;YACV,IAAI,eAAe;YACnB,IAAI,iBAAiB,OAAO;gBAC1B,eAAe,MAAM,OAAO;gBAC5B,IAAI,MAAM,IAAI,KAAK,eAAe,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU;oBACjE,eAAe;gBACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,oBAAoB;oBACpD,eAAe;gBACjB;YACF;YAEA,UAAU,CAAC,MAAM,EAAE,cAAc;QACnC,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAoD;;;;;;0BAClE,8OAAC;gBAAE,WAAU;0BAAiC;;;;;;0BAI9C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;gCAA+C,SAAQ;0CAAW;;;;;;0CAGnF,8OAAC;gCACC,WAAU;gCACV,IAAG;gCACH,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;gCACtC,aAAY;gCACZ,OAAO;;;;;;;;;;;;kCAKX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;;oCAA0C;oCACpD;oCACJ,CAAC,+CACA,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAGpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAW,CAAC,0DAA0D,EACpE,CAAC,iCAAiC,YAC9B,uFAEJ;wCACF,UAAU,CAAC,iCAAiC;wCAC5C,SAAS;kDAER,aAAa,CAAC,KAAK,EAAE,YAAY,GAAG;;;;;;oCAEtC,4BACC,8OAAC;wCACC,WAAU;wCACV,UAAU;wCACV,SAAS;4CACP,kBAAkB;4CAClB,cAAc;4CACd,UAAU;wCACZ;wCACA,OAAM;kDACP;;;;;;;;;;;;4BAKJ,CAAC,+CACA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAMzC,8OAAC;wBACC,WAAW,CAAC,qEAAqE,EAC/E,YACI,mCACA,yGACJ;wBACF,UAAU;wBACV,SAAS;kCAER,YAAY,WAAW;;;;;;oBAGzB,wBACC,8OAAC;wBACC,WAAW,CAAC,uBAAuB,EACjC,OAAO,QAAQ,CAAC,QACZ,wDACA,OAAO,QAAQ,CAAC,SAAS,OAAO,QAAQ,CAAC,QACvC,kDACA,oDACN;kCAED;;;;;;;;;;;;0BAKP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmB;;;;;;kCACjC,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;kCAGN,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,OAAO;kDACvB;;;;;;kDAGD,8OAAC;wCACC,WAAU;wCACV,SAAS,IACP,OAAO;kDAEV;;;;;;;;;;;;;;;;;;kCAML,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,WAAU;4BAAwC,MAAK;sCAAQ;;;;;;;;;;;;;;;;;;;;;;;AAO/E", "debugId": null}}]}