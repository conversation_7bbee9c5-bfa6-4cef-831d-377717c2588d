{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/api/video/info/route.ts"], "sourcesContent": ["import { type NextRequest, NextResponse } from 'next/server'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { url } = await request.json()\n\n    if (!url) {\n      return NextResponse.json({ error: 'URL is required' }, { status: 400 })\n    }\n\n    // 这里需要替换为您的实际后端API地址\n    const backendApiUrl = 'https://proxy.layzz.cn/lyz/platAnalyse/'\n    const body = JSON.stringify({\n      link: url,\n      token: 'uuic-dw-e2-cd19961216',\n    })\n    console.log('body', body)\n    const response = await fetch(backendApiUrl, {\n      body,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      method: 'POST',\n    })\n\n    if (!response.ok) {\n      throw new Error(`Backend API error: ${response.status}`)\n    }\n\n    const data = await response.json()\n    return NextResponse.json(data)\n  } catch (error) {\n    console.error('API Error:', error)\n    return NextResponse.json(\n      {\n        code: '9999',\n        data: null,\n        message: error instanceof Error ? error.message : '服务器内部错误',\n      },\n      { status: 500 },\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,QAAQ,IAAI;QAElC,IAAI,CAAC,KAAK;YACR,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAkB,GAAG;gBAAE,QAAQ;YAAI;QACvE;QAEA,qBAAqB;QACrB,MAAM,gBAAgB;QACtB,MAAM,OAAO,KAAK,SAAS,CAAC;YAC1B,MAAM;YACN,OAAO;QACT;QACA,QAAQ,GAAG,CAAC,QAAQ;QACpB,MAAM,WAAW,MAAM,MAAM,eAAe;YAC1C;YACA,SAAS;gBACP,gBAAgB;YAClB;YACA,QAAQ;QACV;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,SAAS,MAAM,EAAE;QACzD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,MAAM;YACN,MAAM;YACN,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}