{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/components/ui/badge.tsx"], "sourcesContent": ["import { Slot } from '@radix-ui/react-slot'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport type * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst badgeVariants = cva(\n  'inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden',\n  {\n    defaultVariants: {\n      variant: 'default',\n    },\n    variants: {\n      variant: {\n        default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\n        destructive:\n          'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n        outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground',\n        secondary:\n          'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\n      },\n    },\n  },\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : 'span'\n\n  return <Comp className={cn(badgeVariants({ variant }), className)} data-slot='badge' {...props} />\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,iBAAiB;QACf,SAAS;IACX;IACA,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SAAS;YACT,WACE;QACJ;IACF;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OACuF;IAC1F,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBAAO,6LAAC;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAY,aAAU;QAAS,GAAG,KAAK;;;;;;AAChG;KATS", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from '@radix-ui/react-slot'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport type * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    defaultVariants: {\n      size: 'default',\n      variant: 'default',\n    },\n    variants: {\n      size: {\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n        icon: 'size-9',\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\n      },\n      variant: {\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n        link: 'text-primary underline-offset-4 hover:underline',\n        outline:\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n      },\n    },\n  },\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : 'button'\n\n  return (\n    <Comp\n      className={cn(buttonVariants({ className, size, variant }))}\n      data-slot='button'\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,UAAU;QACR,MAAM;YACJ,SAAS;YACT,MAAM;YACN,IAAI;YACJ,IAAI;QACN;QACA,SAAS;YACP,SAAS;YACT,aACE;YACF,OAAO;YACP,MAAM;YACN,SACE;YACF,WAAW;QACb;IACF;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAW;YAAM;QAAQ;QACxD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/components/ui/card.tsx"], "sourcesContent": ["import type * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn(\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\n        className,\n      )}\n      data-slot='card'\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn(\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\n        className,\n      )}\n      data-slot='card-header'\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn('leading-none font-semibold', className)}\n      data-slot='card-title'\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn('text-muted-foreground text-sm', className)}\n      data-slot='card-description'\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)}\n      data-slot='card-action'\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\n  return <div className={cn('px-6', className)} data-slot='card-content' {...props} />\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\n      data-slot='card-footer'\n      {...props}\n    />\n  )\n}\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC5C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAChF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAY,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClF;MAFS;AAIT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACzD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/lib/storage.ts"], "sourcesContent": ["interface CacheData {\n  urls: string\n  results: VideoInfo[]\n  timestamp: number\n}\n\ninterface VideoInfo {\n  id: string\n  name: string\n  cover: string\n  videoUrl: string\n  originalUrl: string\n  success: boolean\n  error?: string\n}\n\nconst STORAGE_KEY = 'video-url-processor-cache'\nconst CACHE_EXPIRY = 30 * 24 * 60 * 60 * 1000 // 30天过期\n\nexport const storage = {\n  // 清除缓存\n  clear: () => {\n    try {\n      localStorage.removeItem(STORAGE_KEY)\n    } catch (error) {\n      console.warn('Failed to clear localStorage:', error)\n    }\n  },\n\n  // 检查localStorage是否可用\n  isAvailable: (): boolean => {\n    try {\n      const test = '__localStorage_test__'\n      localStorage.setItem(test, test)\n      localStorage.removeItem(test)\n      return true\n    } catch {\n      return false\n    }\n  },\n\n  // 从localStorage加载数据\n  load: (): CacheData | null => {\n    try {\n      const stored = localStorage.getItem(STORAGE_KEY)\n      if (!stored) return null\n\n      const data: CacheData = JSON.parse(stored)\n\n      // 检查是否过期\n      if (Date.now() - data.timestamp > CACHE_EXPIRY) {\n        localStorage.removeItem(STORAGE_KEY)\n        return null\n      }\n\n      return data\n    } catch (error) {\n      console.warn('Failed to load from localStorage:', error)\n      return null\n    }\n  },\n  // 保存数据到localStorage\n  save: (urls: string, results: VideoInfo[]) => {\n    try {\n      const data: CacheData = {\n        results,\n        timestamp: Date.now(),\n        urls,\n      }\n      localStorage.setItem(STORAGE_KEY, JSON.stringify(data))\n    } catch (error) {\n      console.warn('Failed to save to localStorage:', error)\n    }\n  },\n}\n"], "names": [], "mappings": ";;;AAgBA,MAAM,cAAc;AACpB,MAAM,eAAe,KAAK,KAAK,KAAK,KAAK,KAAK,QAAQ;;AAE/C,MAAM,UAAU;IACrB,OAAO;IACP,OAAO;QACL,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,iCAAiC;QAChD;IACF;IAEA,qBAAqB;IACrB,aAAa;QACX,IAAI;YACF,MAAM,OAAO;YACb,aAAa,OAAO,CAAC,MAAM;YAC3B,aAAa,UAAU,CAAC;YACxB,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM;QACJ,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,CAAC,QAAQ,OAAO;YAEpB,MAAM,OAAkB,KAAK,KAAK,CAAC;YAEnC,SAAS;YACT,IAAI,KAAK,GAAG,KAAK,KAAK,SAAS,GAAG,cAAc;gBAC9C,aAAa,UAAU,CAAC;gBACxB,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,qCAAqC;YAClD,OAAO;QACT;IACF;IACA,oBAAoB;IACpB,MAAM,CAAC,MAAc;QACnB,IAAI;YACF,MAAM,OAAkB;gBACtB;gBACA,WAAW,KAAK,GAAG;gBACnB;YACF;YACA,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,mCAAmC;QAClD;IACF;AACF", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/components/cache-status.tsx"], "sourcesContent": ["'use client'\n\nimport { Clock, Database, Download, Trash2 } from 'lucide-react'\nimport { useEffect, useState } from 'react'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { storage } from '@/lib/storage'\n\ninterface VideoInfo {\n  id: string\n  name: string\n  cover: string\n  videoUrl: string\n  originalUrl: string\n  success: boolean\n  error?: string\n}\n\ninterface CacheStatusProps {\n  results: VideoInfo[]\n  onClearCache: () => void\n}\n\nexport function CacheStatus({ results, onClearCache }: CacheStatusProps) {\n  const [cacheSize, setCacheSize] = useState<string>('0 KB')\n  const [cacheAvailable, setCacheAvailable] = useState(false)\n\n  useEffect(() => {\n    setCacheAvailable(storage.isAvailable())\n\n    if (storage.isAvailable()) {\n      try {\n        const stored = localStorage.getItem('video-url-processor-cache')\n        if (stored) {\n          const sizeInBytes = new Blob([stored]).size\n          const sizeInKB = (sizeInBytes / 1024).toFixed(2)\n          setCacheSize(`${sizeInKB} KB`)\n        }\n      } catch (error) {\n        console.warn('Failed to calculate cache size:', error)\n      }\n    }\n  }, [results])\n\n  const exportCache = () => {\n    const cachedData = storage.load()\n    if (cachedData) {\n      const dataStr = JSON.stringify(cachedData, null, 2)\n      const dataBlob = new Blob([dataStr], { type: 'application/json' })\n      const url = URL.createObjectURL(dataBlob)\n      const link = document.createElement('a')\n      link.href = url\n      link.download = `video-cache-${new Date().toISOString().split('T')[0]}.json`\n      link.click()\n      URL.revokeObjectURL(url)\n    }\n  }\n\n  if (!cacheAvailable) {\n    return (\n      <Card className='border-yellow-200 bg-yellow-50'>\n        <CardContent className='p-4'>\n          <div className='flex items-center text-yellow-700'>\n            <Database className='h-4 w-4 mr-2' />\n            <span className='text-sm'>localStorage 不可用，无法保存缓存</span>\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  const successCount = results.filter((r) => r.success).length\n  const totalCount = results.length\n\n  return (\n    <Card className='border-blue-200 bg-blue-50'>\n      <CardContent className='p-4'>\n        <div className='flex items-center justify-between'>\n          <div className='flex items-center space-x-4'>\n            <div className='flex items-center text-blue-700'>\n              <Database className='h-4 w-4 mr-2' />\n              <span className='text-sm font-medium'>缓存状态</span>\n            </div>\n            <div className='flex items-center space-x-2'>\n              <Badge className='text-xs' variant='secondary'>\n                <Clock className='h-3 w-3 mr-1' />\n                {cacheSize}\n              </Badge>\n              {totalCount > 0 && (\n                <Badge\n                  className='text-xs'\n                  variant={successCount === totalCount ? 'default' : 'destructive'}\n                >\n                  {successCount}/{totalCount} 成功\n                </Badge>\n              )}\n            </div>\n          </div>\n          <div className='flex items-center space-x-2'>\n            {totalCount > 0 && (\n              <Button\n                className='h-7 px-2 text-xs hover:bg-blue-100'\n                onClick={exportCache}\n                size='sm'\n                variant='ghost'\n              >\n                <Download className='h-3 w-3 mr-1' />\n                导出\n              </Button>\n            )}\n            <Button\n              className='h-7 px-2 text-xs hover:bg-red-100 hover:text-red-600'\n              onClick={onClearCache}\n              size='sm'\n              variant='ghost'\n            >\n              <Trash2 className='h-3 w-3 mr-1' />\n              清除\n            </Button>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAwBO,SAAS,YAAY,EAAE,OAAO,EAAE,YAAY,EAAoB;;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,kBAAkB,wHAAA,CAAA,UAAO,CAAC,WAAW;YAErC,IAAI,wHAAA,CAAA,UAAO,CAAC,WAAW,IAAI;gBACzB,IAAI;oBACF,MAAM,SAAS,aAAa,OAAO,CAAC;oBACpC,IAAI,QAAQ;wBACV,MAAM,cAAc,IAAI,KAAK;4BAAC;yBAAO,EAAE,IAAI;wBAC3C,MAAM,WAAW,CAAC,cAAc,IAAI,EAAE,OAAO,CAAC;wBAC9C,aAAa,GAAG,SAAS,GAAG,CAAC;oBAC/B;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,mCAAmC;gBAClD;YACF;QACF;gCAAG;QAAC;KAAQ;IAEZ,MAAM,cAAc;QAClB,MAAM,aAAa,wHAAA,CAAA,UAAO,CAAC,IAAI;QAC/B,IAAI,YAAY;YACd,MAAM,UAAU,KAAK,SAAS,CAAC,YAAY,MAAM;YACjD,MAAM,WAAW,IAAI,KAAK;gBAAC;aAAQ,EAAE;gBAAE,MAAM;YAAmB;YAChE,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC5E,KAAK,KAAK;YACV,IAAI,eAAe,CAAC;QACtB;IACF;IAEA,IAAI,CAAC,gBAAgB;QACnB,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;IAKpC;IAEA,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC,IAAM,EAAE,OAAO,EAAE,MAAM;IAC5D,MAAM,aAAa,QAAQ,MAAM;IAEjC,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAExC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;wCAAU,SAAQ;;0DACjC,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB;;;;;;;oCAEF,aAAa,mBACZ,6LAAC,oIAAA,CAAA,QAAK;wCACJ,WAAU;wCACV,SAAS,iBAAiB,aAAa,YAAY;;4CAElD;4CAAa;4CAAE;4CAAW;;;;;;;;;;;;;;;;;;;kCAKnC,6LAAC;wBAAI,WAAU;;4BACZ,aAAa,mBACZ,6LAAC,qIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS;gCACT,MAAK;gCACL,SAAQ;;kDAER,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIzC,6LAAC,qIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS;gCACT,MAAK;gCACL,SAAQ;;kDAER,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GArGgB;KAAA", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/components/ui/progress.tsx"], "sourcesContent": ["'use client'\n\nimport * as ProgressPrimitive from '@radix-ui/react-progress'\nimport type * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      className={cn('bg-primary/20 relative h-2 w-full overflow-hidden rounded-full', className)}\n      data-slot='progress'\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        className='bg-primary h-full w-full flex-1 transition-all'\n        data-slot='progress-indicator'\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAChF,aAAU;QACT,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,aAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KAlBS", "debugId": null}}, {"offset": {"line": 633, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/components/ui/scroll-area.tsx"], "sourcesContent": ["'use client'\n\nimport * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area'\nimport type * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return (\n    <ScrollAreaPrimitive.Root\n      className={cn('relative', className)}\n      data-slot='scroll-area'\n      {...props}\n    >\n      <ScrollAreaPrimitive.Viewport\n        className='focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1'\n        data-slot='scroll-area-viewport'\n      >\n        {children}\n      </ScrollAreaPrimitive.Viewport>\n      <ScrollBar />\n      <ScrollAreaPrimitive.Corner />\n    </ScrollAreaPrimitive.Root>\n  )\n}\n\nfunction ScrollBar({\n  className,\n  orientation = 'vertical',\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return (\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\n      className={cn(\n        'flex touch-none p-px transition-colors select-none',\n        orientation === 'vertical' && 'h-full w-2.5 border-l border-l-transparent',\n        orientation === 'horizontal' && 'h-2.5 flex-col border-t border-t-transparent',\n        className,\n      )}\n      data-slot='scroll-area-scrollbar'\n      orientation={orientation}\n      {...props}\n    >\n      <ScrollAreaPrimitive.ScrollAreaThumb\n        className='bg-border relative flex-1 rounded-full'\n        data-slot='scroll-area-thumb'\n      />\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\n  )\n}\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,aAAU;QACT,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAC3B,WAAU;gBACV,aAAU;0BAET;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;KArBS;AAuBT,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cAAc,8CAC9B,gBAAgB,gBAAgB,gDAChC;QAEF,aAAU;QACV,aAAa;QACZ,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAClC,WAAU;YACV,aAAU;;;;;;;;;;;AAIlB;MAvBS", "debugId": null}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/components/download-progress.tsx"], "sourcesContent": ["'use client'\n\nimport { Check<PERSON><PERSON><PERSON>, Clock, Download, X, XCircle } from 'lucide-react'\nimport { useEffect, useState } from 'react'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Progress } from '@/components/ui/progress'\nimport { ScrollArea } from '@/components/ui/scroll-area'\n\ninterface DownloadProgress {\n  id: string\n  name: string\n  status: 'pending' | 'downloading' | 'completed' | 'failed'\n  progress: number\n  error?: string\n}\n\ninterface DownloadProgressProps {\n  progress: DownloadProgress[]\n  onClose: () => void\n}\n\nexport function DownloadProgressModal({ progress, onClose }: DownloadProgressProps) {\n  const [isVisible, setIsVisible] = useState(false)\n\n  useEffect(() => {\n    setIsVisible(progress.length > 0)\n  }, [progress])\n\n  if (!isVisible) return null\n\n  const completedCount = progress.filter((p) => p.status === 'completed').length\n  const failedCount = progress.filter((p) => p.status === 'failed').length\n  const downloadingCount = progress.filter((p) => p.status === 'downloading').length\n  const pendingCount = progress.filter((p) => p.status === 'pending').length\n\n  const getStatusIcon = (status: DownloadProgress['status']) => {\n    switch (status) {\n      case 'completed':\n        return <CheckCircle className='h-4 w-4 text-green-600' />\n      case 'failed':\n        return <XCircle className='h-4 w-4 text-red-600' />\n      case 'downloading':\n        return <Download className='h-4 w-4 text-blue-600 animate-pulse' />\n      case 'pending':\n        return <Clock className='h-4 w-4 text-gray-400' />\n    }\n  }\n\n  const getStatusColor = (status: DownloadProgress['status']) => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-500'\n      case 'failed':\n        return 'bg-red-500'\n      case 'downloading':\n        return 'bg-blue-500'\n      case 'pending':\n        return 'bg-gray-300'\n    }\n  }\n\n  return (\n    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>\n      <Card className='w-full max-w-2xl max-h-[80vh] flex flex-col'>\n        <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>\n          <CardTitle className='text-lg font-semibold'>下载进度</CardTitle>\n          <Button className='h-8 w-8 p-0' onClick={onClose} size='sm' variant='ghost'>\n            <X className='h-4 w-4' />\n          </Button>\n        </CardHeader>\n        <CardContent className='flex-1 overflow-hidden'>\n          <div className='space-y-4'>\n            {/* 统计信息 */}\n            <div className='flex items-center space-x-4 text-sm'>\n              <Badge variant='secondary'>总计: {progress.length}</Badge>\n              {completedCount > 0 && (\n                <Badge className='bg-green-100 text-green-800'>完成: {completedCount}</Badge>\n              )}\n              {downloadingCount > 0 && (\n                <Badge className='bg-blue-100 text-blue-800'>下载中: {downloadingCount}</Badge>\n              )}\n              {pendingCount > 0 && <Badge variant='outline'>等待: {pendingCount}</Badge>}\n              {failedCount > 0 && <Badge variant='destructive'>失败: {failedCount}</Badge>}\n            </div>\n\n            {/* 总体进度 */}\n            <div className='space-y-2'>\n              <div className='flex justify-between text-sm'>\n                <span>总体进度</span>\n                <span>{Math.round((completedCount / progress.length) * 100)}%</span>\n              </div>\n              <Progress className='h-2' value={(completedCount / progress.length) * 100} />\n            </div>\n\n            {/* 详细进度列表 */}\n            <ScrollArea className='h-64'>\n              <div className='space-y-3'>\n                {progress.map((item) => (\n                  <div className='border rounded-lg p-3 space-y-2' key={item.id}>\n                    <div className='flex items-center justify-between'>\n                      <div className='flex items-center space-x-2 flex-1 min-w-0'>\n                        {getStatusIcon(item.status)}\n                        <span className='text-sm font-medium truncate' title={item.name}>\n                          {item.name}\n                        </span>\n                      </div>\n                      <Badge className='text-xs' variant='outline'>\n                        {item.status === 'downloading' ? `${item.progress}%` : item.status}\n                      </Badge>\n                    </div>\n\n                    {item.status === 'downloading' && (\n                      <Progress\n                        className={`h-1 ${getStatusColor(item.status)}`}\n                        value={item.progress}\n                      />\n                    )}\n\n                    {item.status === 'failed' && item.error && (\n                      <p className='text-xs text-red-600 bg-red-50 p-2 rounded'>{item.error}</p>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </ScrollArea>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAuBO,SAAS,sBAAsB,EAAE,QAAQ,EAAE,OAAO,EAAyB;;IAChF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,aAAa,SAAS,MAAM,GAAG;QACjC;0CAAG;QAAC;KAAS;IAEb,IAAI,CAAC,WAAW,OAAO;IAEvB,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,aAAa,MAAM;IAC9E,MAAM,cAAc,SAAS,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,UAAU,MAAM;IACxE,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,eAAe,MAAM;IAClF,MAAM,eAAe,SAAS,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,WAAW,MAAM;IAE1E,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAwB;;;;;;sCAC7C,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;4BAAc,SAAS;4BAAS,MAAK;4BAAK,SAAQ;sCAClE,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAGjB,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAY;4CAAK,SAAS,MAAM;;;;;;;oCAC9C,iBAAiB,mBAChB,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;;4CAA8B;4CAAK;;;;;;;oCAErD,mBAAmB,mBAClB,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;;4CAA4B;4CAAM;;;;;;;oCAEpD,eAAe,mBAAK,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAU;4CAAK;;;;;;;oCAClD,cAAc,mBAAK,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAc;4CAAK;;;;;;;;;;;;;0CAIxD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;;oDAAM,KAAK,KAAK,CAAC,AAAC,iBAAiB,SAAS,MAAM,GAAI;oDAAK;;;;;;;;;;;;;kDAE9D,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAM,OAAO,AAAC,iBAAiB,SAAS,MAAM,GAAI;;;;;;;;;;;;0CAIxE,6LAAC,6IAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,cAAc,KAAK,MAAM;8EAC1B,6LAAC;oEAAK,WAAU;oEAA+B,OAAO,KAAK,IAAI;8EAC5D,KAAK,IAAI;;;;;;;;;;;;sEAGd,6LAAC,oIAAA,CAAA,QAAK;4DAAC,WAAU;4DAAU,SAAQ;sEAChC,KAAK,MAAM,KAAK,gBAAgB,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,KAAK,MAAM;;;;;;;;;;;;gDAIrE,KAAK,MAAM,KAAK,+BACf,6LAAC,uIAAA,CAAA,WAAQ;oDACP,WAAW,CAAC,IAAI,EAAE,eAAe,KAAK,MAAM,GAAG;oDAC/C,OAAO,KAAK,QAAQ;;;;;;gDAIvB,KAAK,MAAM,KAAK,YAAY,KAAK,KAAK,kBACrC,6LAAC;oDAAE,WAAU;8DAA8C,KAAK,KAAK;;;;;;;2CArBnB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgC/E;GA7GgB;KAAA", "debugId": null}}, {"offset": {"line": 1062, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/components/ui/textarea.tsx"], "sourcesContent": ["import type * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nfunction Textarea({ className, ...props }: React.ComponentProps<'textarea'>) {\n  return (\n    <textarea\n      className={cn(\n        'border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n        className,\n      )}\n      data-slot='textarea'\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAEF,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 1093, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/components/ui/tooltip.tsx"], "sourcesContent": ["'use client'\n\nimport * as TooltipPrimitive from '@radix-ui/react-tooltip'\nimport type * as React from 'react'\n\nimport { cn } from '@/lib/utils'\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot='tooltip-provider'\n      delayDuration={delayDuration}\n      {...props}\n    />\n  )\n}\n\nfunction Tooltip({ ...props }: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot='tooltip' {...props} />\n    </TooltipProvider>\n  )\n}\n\nfunction TooltipTrigger({ ...props }: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot='tooltip-trigger' {...props} />\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        className={cn(\n          'bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance',\n          className,\n        )}\n        data-slot='tooltip-content'\n        sideOffset={sideOffset}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className='bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]' />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  )\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,6LAAC,sKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC/E,qBACE,6LAAC;kBACC,cAAA,6LAAC,sKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MANS;AAQT,SAAS,eAAe,EAAE,GAAG,OAA8D;IACzF,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAFS;AAIT,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAEF,aAAU;YACV,YAAY;YACX,GAAG,KAAK;;gBAER;8BACD,6LAAC,sKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 1190, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/components/url-tooltip.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON><PERSON>, <PERSON> } from 'lucide-react'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'\n\ninterface UrlTooltipProps {\n  urls: string[]\n  count: number\n}\n\nexport function UrlTooltip({ urls, count }: UrlTooltipProps) {\n  const copyToClipboard = (url: string) => {\n    navigator.clipboard.writeText(url).catch(() => {\n      // 静默处理复制失败\n    })\n  }\n\n  const copyAllUrls = () => {\n    const allUrls = urls.join('\\n')\n    navigator.clipboard.writeText(allUrls).catch(() => {\n      // 静默处理复制失败\n    })\n  }\n\n  const formatUrl = (url: string, maxLength = 45) => {\n    if (url.length <= maxLength) return url\n\n    try {\n      const urlObj = new URL(url)\n      const domain = urlObj.hostname\n      const path = urlObj.pathname + urlObj.search\n\n      if (domain.length + path.length <= maxLength) {\n        return `${domain}${path}`\n      }\n\n      const availablePathLength = maxLength - domain.length - 3\n      if (availablePathLength > 10) {\n        return `${domain}${path.substring(0, availablePathLength)}...`\n      }\n\n      return `${domain}...`\n    } catch {\n      return url.length > maxLength ? `${url.substring(0, maxLength - 3)}...` : url\n    }\n  }\n\n  if (count === 0) return null\n\n  return (\n    <TooltipProvider>\n      <Tooltip>\n        <TooltipTrigger asChild>\n          <Badge className='cursor-help hover:bg-blue-100 transition-colors' variant='secondary'>\n            <Link className='h-3 w-3 mr-1' />\n            检测到 {count} 个有效URL\n          </Badge>\n        </TooltipTrigger>\n        <TooltipContent className='max-w-lg p-0' side='top'>\n          <div className='p-3'>\n            <div className='flex items-center justify-between mb-2'>\n              <p className='font-medium text-sm text-gray-900'>有效URL列表</p>\n              {urls.length > 1 && (\n                <Button\n                  className='h-6 px-2 text-xs hover:bg-gray-100'\n                  onClick={copyAllUrls}\n                  size='sm'\n                  variant='ghost'\n                >\n                  <Copy className='h-3 w-3 mr-1' />\n                  全部复制\n                </Button>\n              )}\n            </div>\n            <ScrollArea className='max-h-48'>\n              <div className='space-y-1'>\n                {urls.slice(0, 15).map((url, index) => (\n                  <div\n                    className='group flex items-center justify-between bg-gray-50 hover:bg-gray-100 p-2 rounded text-xs transition-colors'\n                    key={index}\n                  >\n                    <div className='flex-1 min-w-0'>\n                      <div className='font-mono text-gray-700 overflow-hidden whitespace-nowrap'>\n                        {formatUrl(url)}\n                      </div>\n                      {url !== formatUrl(url) && (\n                        <div className='text-gray-500 mt-1 text-[10px]' title={url}>\n                          {url}\n                        </div>\n                      )}\n                    </div>\n                    <Button\n                      className='h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity ml-2 hover:bg-gray-200'\n                      onClick={() => copyToClipboard(url)}\n                      size='sm'\n                      variant='ghost'\n                    >\n                      <Copy className='h-3 w-3' />\n                    </Button>\n                  </div>\n                ))}\n                {urls.length > 15 && (\n                  <div className='text-center py-2'>\n                    <Badge className='text-xs' variant='outline'>\n                      还有 {urls.length - 15} 个URL未显示\n                    </Badge>\n                  </div>\n                )}\n              </div>\n            </ScrollArea>\n          </div>\n        </TooltipContent>\n      </Tooltip>\n    </TooltipProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAaO,SAAS,WAAW,EAAE,IAAI,EAAE,KAAK,EAAmB;IACzD,MAAM,kBAAkB,CAAC;QACvB,UAAU,SAAS,CAAC,SAAS,CAAC,KAAK,KAAK,CAAC;QACvC,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,UAAU,KAAK,IAAI,CAAC;QAC1B,UAAU,SAAS,CAAC,SAAS,CAAC,SAAS,KAAK,CAAC;QAC3C,WAAW;QACb;IACF;IAEA,MAAM,YAAY,CAAC,KAAa,YAAY,EAAE;QAC5C,IAAI,IAAI,MAAM,IAAI,WAAW,OAAO;QAEpC,IAAI;YACF,MAAM,SAAS,IAAI,IAAI;YACvB,MAAM,SAAS,OAAO,QAAQ;YAC9B,MAAM,OAAO,OAAO,QAAQ,GAAG,OAAO,MAAM;YAE5C,IAAI,OAAO,MAAM,GAAG,KAAK,MAAM,IAAI,WAAW;gBAC5C,OAAO,GAAG,SAAS,MAAM;YAC3B;YAEA,MAAM,sBAAsB,YAAY,OAAO,MAAM,GAAG;YACxD,IAAI,sBAAsB,IAAI;gBAC5B,OAAO,GAAG,SAAS,KAAK,SAAS,CAAC,GAAG,qBAAqB,GAAG,CAAC;YAChE;YAEA,OAAO,GAAG,OAAO,GAAG,CAAC;QACvB,EAAE,OAAM;YACN,OAAO,IAAI,MAAM,GAAG,YAAY,GAAG,IAAI,SAAS,CAAC,GAAG,YAAY,GAAG,GAAG,CAAC,GAAG;QAC5E;IACF;IAEA,IAAI,UAAU,GAAG,OAAO;IAExB,qBACE,6LAAC,sIAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;8BACN,6LAAC,sIAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,6LAAC,oIAAA,CAAA,QAAK;wBAAC,WAAU;wBAAkD,SAAQ;;0CACzE,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;4BAC5B;4BAAM;;;;;;;;;;;;8BAGf,6LAAC,sIAAA,CAAA,iBAAc;oBAAC,WAAU;oBAAe,MAAK;8BAC5C,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;oCAChD,KAAK,MAAM,GAAG,mBACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS;wCACT,MAAK;wCACL,SAAQ;;0DAER,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAKvC,6LAAC,6IAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,KAAK,sBAC3B,6LAAC;gDACC,WAAU;;kEAGV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,UAAU;;;;;;4DAEZ,QAAQ,UAAU,sBACjB,6LAAC;gEAAI,WAAU;gEAAiC,OAAO;0EACpD;;;;;;;;;;;;kEAIP,6LAAC,qIAAA,CAAA,SAAM;wDACL,WAAU;wDACV,SAAS,IAAM,gBAAgB;wDAC/B,MAAK;wDACL,SAAQ;kEAER,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;+CAlBb;;;;;wCAsBR,KAAK,MAAM,GAAG,oBACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;gDAAU,SAAQ;;oDAAU;oDACvC,KAAK,MAAM,GAAG;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3C;KAzGgB", "debugId": null}}, {"offset": {"line": 1434, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/lib/download.ts"], "sourcesContent": ["interface DownloadProgress {\n  id: string\n  name: string\n  status: 'pending' | 'downloading' | 'completed' | 'failed'\n  progress: number\n  error?: string\n}\n\nexport class VideoDownloader {\n  private downloadQueue: DownloadProgress[] = []\n  private onProgressUpdate?: (progress: DownloadProgress[]) => void\n\n  constructor(onProgressUpdate?: (progress: DownloadProgress[]) => void) {\n    this.onProgressUpdate = onProgressUpdate\n  }\n\n  // 下载单个视频\n  async downloadVideo(videoUrl: string, fileName: string, videoId: string): Promise<boolean> {\n    try {\n      // 更新下载状态\n      this.updateProgress(videoId, 'downloading', 0)\n\n      const response = await fetch(videoUrl, {\n        headers: {\n          Accept: 'video/*',\n        },\n        method: 'GET',\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`)\n      }\n\n      const contentLength = response.headers.get('content-length')\n      const total = contentLength ? Number.parseInt(contentLength, 10) : 0\n\n      const reader = response.body?.getReader()\n      if (!reader) {\n        throw new Error('无法读取视频流')\n      }\n\n      const chunks: Uint8Array[] = []\n      let receivedLength = 0\n\n      while (true) {\n        const { done, value } = await reader.read()\n\n        if (done) break\n\n        chunks.push(value)\n        receivedLength += value.length\n\n        // 更新进度\n        if (total > 0) {\n          const progress = Math.round((receivedLength / total) * 100)\n          this.updateProgress(videoId, 'downloading', progress)\n        }\n      }\n\n      // 合并所有chunks\n      const blob = new Blob(chunks, { type: 'video/mp4' })\n\n      // 创建下载链接\n      const url = URL.createObjectURL(blob)\n      const link = document.createElement('a')\n      link.href = url\n      link.download = this.sanitizeFileName(fileName)\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n      URL.revokeObjectURL(url)\n\n      this.updateProgress(videoId, 'completed', 100)\n      return true\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : '下载失败'\n      this.updateProgress(videoId, 'failed', 0, errorMessage)\n      return false\n    }\n  }\n\n  // 批量下载视频\n  async downloadVideos(\n    videos: Array<{ id: string; name: string; videoUrl: string }>,\n    concurrency = 3,\n  ): Promise<void> {\n    // 初始化下载队列\n    this.downloadQueue = videos.map((video) => ({\n      id: video.id,\n      name: video.name,\n      progress: 0,\n      status: 'pending' as const,\n    }))\n\n    this.notifyProgress()\n\n    // 分批下载，控制并发数\n    const chunks = this.chunkArray(videos, concurrency)\n\n    for (const chunk of chunks) {\n      const promises = chunk.map((video) =>\n        this.downloadVideo(video.videoUrl, video.name, video.id),\n      )\n\n      await Promise.allSettled(promises)\n\n      // 添加延迟避免过于频繁的请求\n      await new Promise((resolve) => setTimeout(resolve, 1000))\n    }\n  }\n\n  private updateProgress(\n    id: string,\n    status: DownloadProgress['status'],\n    progress: number,\n    error?: string,\n  ) {\n    const item = this.downloadQueue.find((item) => item.id === id)\n    if (item) {\n      item.status = status\n      item.progress = progress\n      if (error) item.error = error\n      this.notifyProgress()\n    }\n  }\n\n  private notifyProgress() {\n    if (this.onProgressUpdate) {\n      this.onProgressUpdate([...this.downloadQueue])\n    }\n  }\n\n  private sanitizeFileName(fileName: string): string {\n    // 清理文件名，移除不安全字符\n    return (\n      fileName\n        .replace(/[<>:\"/\\\\|?*]/g, '_')\n        .replace(/\\s+/g, '_')\n        .substring(0, 100) + '.mp4'\n    )\n  }\n\n  private chunkArray<T>(array: T[], size: number): T[][] {\n    const chunks: T[][] = []\n    for (let i = 0; i < array.length; i += size) {\n      chunks.push(array.slice(i, i + size))\n    }\n    return chunks\n  }\n\n  getProgress(): DownloadProgress[] {\n    return [...this.downloadQueue]\n  }\n\n  clearProgress() {\n    this.downloadQueue = []\n    this.notifyProgress()\n  }\n}\n"], "names": [], "mappings": ";;;AAQO,MAAM;IACH,gBAAoC,EAAE,CAAA;IACtC,iBAAyD;IAEjE,YAAY,gBAAyD,CAAE;QACrE,IAAI,CAAC,gBAAgB,GAAG;IAC1B;IAEA,SAAS;IACT,MAAM,cAAc,QAAgB,EAAE,QAAgB,EAAE,OAAe,EAAoB;QACzF,IAAI;YACF,SAAS;YACT,IAAI,CAAC,cAAc,CAAC,SAAS,eAAe;YAE5C,MAAM,WAAW,MAAM,MAAM,UAAU;gBACrC,SAAS;oBACP,QAAQ;gBACV;gBACA,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,gBAAgB,SAAS,OAAO,CAAC,GAAG,CAAC;YAC3C,MAAM,QAAQ,gBAAgB,OAAO,QAAQ,CAAC,eAAe,MAAM;YAEnE,MAAM,SAAS,SAAS,IAAI,EAAE;YAC9B,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAuB,EAAE;YAC/B,IAAI,iBAAiB;YAErB,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBAEzC,IAAI,MAAM;gBAEV,OAAO,IAAI,CAAC;gBACZ,kBAAkB,MAAM,MAAM;gBAE9B,OAAO;gBACP,IAAI,QAAQ,GAAG;oBACb,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,iBAAiB,QAAS;oBACvD,IAAI,CAAC,cAAc,CAAC,SAAS,eAAe;gBAC9C;YACF;YAEA,aAAa;YACb,MAAM,OAAO,IAAI,KAAK,QAAQ;gBAAE,MAAM;YAAY;YAElD,SAAS;YACT,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;YACtC,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC;YAEpB,IAAI,CAAC,cAAc,CAAC,SAAS,aAAa;YAC1C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,IAAI,CAAC,cAAc,CAAC,SAAS,UAAU,GAAG;YAC1C,OAAO;QACT;IACF;IAEA,SAAS;IACT,MAAM,eACJ,MAA6D,EAC7D,cAAc,CAAC,EACA;QACf,UAAU;QACV,IAAI,CAAC,aAAa,GAAG,OAAO,GAAG,CAAC,CAAC,QAAU,CAAC;gBAC1C,IAAI,MAAM,EAAE;gBACZ,MAAM,MAAM,IAAI;gBAChB,UAAU;gBACV,QAAQ;YACV,CAAC;QAED,IAAI,CAAC,cAAc;QAEnB,aAAa;QACb,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC,QAAQ;QAEvC,KAAK,MAAM,SAAS,OAAQ;YAC1B,MAAM,WAAW,MAAM,GAAG,CAAC,CAAC,QAC1B,IAAI,CAAC,aAAa,CAAC,MAAM,QAAQ,EAAE,MAAM,IAAI,EAAE,MAAM,EAAE;YAGzD,MAAM,QAAQ,UAAU,CAAC;YAEzB,gBAAgB;YAChB,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;QACrD;IACF;IAEQ,eACN,EAAU,EACV,MAAkC,EAClC,QAAgB,EAChB,KAAc,EACd;QACA,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;QAC3D,IAAI,MAAM;YACR,KAAK,MAAM,GAAG;YACd,KAAK,QAAQ,GAAG;YAChB,IAAI,OAAO,KAAK,KAAK,GAAG;YACxB,IAAI,CAAC,cAAc;QACrB;IACF;IAEQ,iBAAiB;QACvB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC;mBAAI,IAAI,CAAC,aAAa;aAAC;QAC/C;IACF;IAEQ,iBAAiB,QAAgB,EAAU;QACjD,gBAAgB;QAChB,OACE,SACG,OAAO,CAAC,iBAAiB,KACzB,OAAO,CAAC,QAAQ,KAChB,SAAS,CAAC,GAAG,OAAO;IAE3B;IAEQ,WAAc,KAAU,EAAE,IAAY,EAAS;QACrD,MAAM,SAAgB,EAAE;QACxB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,KAAM;YAC3C,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI;QACjC;QACA,OAAO;IACT;IAEA,cAAkC;QAChC,OAAO;eAAI,IAAI,CAAC,aAAa;SAAC;IAChC;IAEA,gBAAgB;QACd,IAAI,CAAC,aAAa,GAAG,EAAE;QACvB,IAAI,CAAC,cAAc;IACrB;AACF", "debugId": null}}, {"offset": {"line": 1562, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/WebstormProjects/f2-desktop/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  Download,\n  DownloadCloud,\n  ExternalLink,\n  Loader2,\n  Play,\n  Refresh<PERSON>w,\n  Trash2,\n} from 'lucide-react'\nimport Image from 'next/image'\nimport { useEffect, useState } from 'react'\nimport { CacheStatus } from '@/components/cache-status'\nimport { DownloadProgressModal } from '@/components/download-progress'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Textarea } from '@/components/ui/textarea'\nimport { UrlTooltip } from '@/components/url-tooltip'\nimport { VideoDownloader } from '@/lib/download'\nimport { storage } from '@/lib/storage'\n\ninterface VideoInfo {\n  id: string\n  name: string\n  cover: string\n  videoUrl: string\n  originalUrl: string\n  success: boolean\n  error?: string\n}\n\ninterface ApiResponse {\n  code: string\n  message: string\n  data: {\n    playAddr: string\n    cover: string\n    desc: string\n  }\n}\n\ninterface DownloadProgress {\n  id: string\n  name: string\n  status: 'pending' | 'downloading' | 'completed' | 'failed'\n  progress: number\n  error?: string\n}\n\nexport default function VideoUrlProcessor() {\n  const [urls, setUrls] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [results, setResults] = useState<VideoInfo[]>([])\n  const [error, setError] = useState('')\n  const [cacheAvailable, setCacheAvailable] = useState(false)\n  const [lastCacheTime, setLastCacheTime] = useState<string>('')\n  const [downloadProgress, setDownloadProgress] = useState<DownloadProgress[]>([])\n  const [downloader, setDownloader] = useState<VideoDownloader | null>(null)\n  const [singleDownloading, setSingleDownloading] = useState<Set<string>>(new Set())\n\n  // 分割URL的函数\n  const parseUrls = (input: string): string[] => {\n    const list = input\n      .split(/[,\\n]/) // 按逗号、斜杠或换行符分割\n      .map((url) => url.trim())\n      .filter((url) => url.length > 0)\n    return [...new Set(list)]\n  }\n\n  // 获取有效URL列表（用于显示）\n  const getValidUrls = (input: string): string[] => {\n    return parseUrls(input)\n  }\n\n  // 模拟API调用\n  const fetchVideoInfo = async (url: string): Promise<VideoInfo> => {\n    try {\n      // 这里需要替换为您的实际API地址\n      const apiUrl = '/api/video/info' // 或者您的实际API地址\n\n      const response = await fetch(apiUrl, {\n        body: JSON.stringify({ url }),\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        method: 'POST',\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`)\n      }\n\n      const result: ApiResponse = await response.json()\n\n      // 检查业务状态码\n      if (result.code !== '0001') {\n        throw new Error(result.message || '获取视频信息失败')\n      }\n\n      const videoId = Math.random().toString(36).substr(2, 9)\n      return {\n        cover: result.data.cover,\n        id: videoId,\n        name: result.data.desc || '未知视频',\n        originalUrl: url,\n        success: true,\n        videoUrl: result.data.playAddr,\n      }\n    } catch (error) {\n      const videoId = Math.random().toString(36).substr(2, 9)\n      return {\n        cover: '/placeholder.svg?height=180&width=320',\n        error: error instanceof Error ? error.message : '未知错误',\n        id: videoId,\n        name: '获取失败',\n        originalUrl: url,\n        success: false,\n        videoUrl: '',\n      }\n    }\n  }\n\n  useEffect(() => {\n    // 检查localStorage是否可用\n    setCacheAvailable(storage.isAvailable())\n\n    // 加载缓存数据\n    const cachedData = storage.load()\n    if (cachedData) {\n      setUrls(cachedData.urls)\n      setResults(cachedData.results)\n      setLastCacheTime(new Date(cachedData.timestamp).toLocaleString())\n    }\n\n    // 初始化下载器\n    const downloaderInstance = new VideoDownloader((progress) => {\n      setDownloadProgress(progress)\n    })\n    setDownloader(downloaderInstance)\n  }, [])\n\n  // 添加保存缓存的函数\n  const saveToCache = (urlsToSave: string, resultsToSave: VideoInfo[]) => {\n    if (cacheAvailable) {\n      storage.save(urlsToSave, resultsToSave)\n      setLastCacheTime(new Date().toLocaleString())\n    }\n  }\n\n  // 添加清除缓存的函数\n  const clearCache = () => {\n    storage.clear()\n    setUrls('')\n    setResults([])\n    setLastCacheTime('')\n    setError('')\n  }\n\n  const handleSubmit = async () => {\n    const urlList = parseUrls(urls)\n\n    if (urlList.length === 0) {\n      setError('请输入有效的URL地址')\n      return\n    }\n\n    setLoading(true)\n    setError('')\n    setResults([])\n\n    try {\n      // 并发调用所有URL的接口\n      const promises = urlList.map((url) => fetchVideoInfo(url))\n      const videoInfos = await Promise.all(promises)\n\n      // 分离成功和失败的结果\n      const successCount = videoInfos.filter((v) => v.success).length\n      const failCount = videoInfos.length - successCount\n\n      setResults(videoInfos)\n\n      // 保存到缓存\n      saveToCache(urls, videoInfos)\n\n      if (failCount > 0) {\n        setError(`${successCount} 个成功，${failCount} 个失败`)\n      }\n    } catch (err) {\n      setError('获取视频信息失败，请重试')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // 批量下载所有视频\n  const handleBatchDownload = async () => {\n    const successfulVideos = results.filter((video) => video.success && video.videoUrl)\n\n    if (successfulVideos.length === 0) {\n      setError('没有可下载的视频')\n      return\n    }\n\n    if (downloader) {\n      await downloader.downloadVideos(\n        successfulVideos.map((video) => ({\n          id: video.id,\n          name: video.name,\n          videoUrl: video.videoUrl,\n        })),\n      )\n    }\n  }\n\n  // 单个视频下载\n  const handleSingleDownload = async (video: VideoInfo) => {\n    if (!video.videoUrl || !downloader) return\n\n    setSingleDownloading((prev) => new Set(prev).add(video.id))\n\n    try {\n      await downloader.downloadVideo(video.videoUrl, video.name, video.id)\n    } finally {\n      setSingleDownloading((prev) => {\n        const newSet = new Set(prev)\n        newSet.delete(video.id)\n        return newSet\n      })\n    }\n  }\n\n  const validUrlCount = parseUrls(urls).length\n  const validUrls = getValidUrls(urls)\n  const successfulVideos = results.filter((video) => video.success && video.videoUrl)\n\n  return (\n    <div className='min-h-screen bg-gray-50 py-8'>\n      <div className='container mx-auto px-4 max-w-4xl'>\n        <div className='text-center mb-8'>\n          <h1 className='text-3xl font-bold text-gray-900 mb-2'>视频信息提取器</h1>\n          <p className='text-gray-600'>输入多个视频URL地址，获取视频名称、封面和地址信息</p>\n        </div>\n\n        <Card className='mb-8'>\n          <CardHeader>\n            <CardTitle>输入视频URL</CardTitle>\n            <CardDescription>支持用逗号(,)或换行符分割多个URL地址</CardDescription>\n          </CardHeader>\n          <CardContent className='space-y-4'>\n            <div className='space-y-2'>\n              <Textarea\n                className='min-h-[120px] resize-none'\n                disabled={loading}\n                onChange={(e) => {\n                  setUrls(e.target.value)\n                  // 延迟保存输入内容到缓存\n                  if (cacheAvailable) {\n                    setTimeout(() => {\n                      storage.save(e.target.value, results)\n                    }, 1000) // 1秒后保存，避免频繁写入\n                  }\n                }}\n                placeholder='请输入视频URL地址，例如：&#10;https://example.com/video1&#10;https://example.com/video2,https://example.com/video3&#10;https://example.com/video4/https://example.com/video5'\n                value={urls}\n              />\n              <div className='flex items-center justify-between text-sm text-gray-500'>\n                <UrlTooltip count={validUrlCount} urls={getValidUrls(urls)} />\n                <span>{urls.length}/2000</span>\n              </div>\n            </div>\n\n            {cacheAvailable && (\n              <div className='flex items-center justify-between text-xs text-gray-500 bg-gray-50 p-3 rounded-md'>\n                <div className='flex items-center space-x-4'>\n                  <span className='flex items-center'>\n                    <RefreshCw className='h-3 w-3 mr-1' />\n                    缓存已启用\n                  </span>\n                  {lastCacheTime && <span>最后更新: {lastCacheTime}</span>}\n                </div>\n                <Button\n                  className='h-6 px-2 text-xs hover:bg-red-50 hover:text-red-600'\n                  onClick={clearCache}\n                  size='sm'\n                  variant='ghost'\n                >\n                  <Trash2 className='h-3 w-3 mr-1' />\n                  清除缓存\n                </Button>\n              </div>\n            )}\n\n            {error && <div className='text-red-600 text-sm bg-red-50 p-3 rounded-md'>{error}</div>}\n\n            <Button\n              className='w-full'\n              disabled={loading || validUrlCount === 0}\n              onClick={handleSubmit}\n            >\n              {loading ? (\n                <>\n                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />\n                  正在获取视频信息...\n                </>\n              ) : (\n                `获取视频信息 (${validUrlCount} 个URL)`\n              )}\n            </Button>\n          </CardContent>\n        </Card>\n\n        {/* 在输入Card后添加 */}\n        <CacheStatus onClearCache={clearCache} results={results} />\n\n        {results.length > 0 && (\n          <div className='space-y-4'>\n            <div className='flex items-center justify-between'>\n              <h2 className='text-2xl font-semibold text-gray-900'>\n                视频信息结果 ({results.length})\n              </h2>\n              {successfulVideos.length > 0 && (\n                <Button\n                  className='bg-green-600 hover:bg-green-700 text-white'\n                  disabled={downloadProgress.some((p) => p.status === 'downloading')}\n                  onClick={handleBatchDownload}\n                >\n                  <DownloadCloud className='mr-2 h-4 w-4' />\n                  批量下载 ({successfulVideos.length} 个视频)\n                </Button>\n              )}\n            </div>\n            <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>\n              {results.map((video) => (\n                <Card\n                  className={`overflow-hidden hover:shadow-lg transition-shadow ${!video.success ? 'border-red-200 bg-red-50' : ''}`}\n                  key={video.id}\n                >\n                  <div className='relative'>\n                    <Image\n                      alt={video.name}\n                      className='w-full h-48 object-cover'\n                      height={180}\n                      src={video.cover || '/placeholder.svg'}\n                      width={320}\n                    />\n                    {!video.success && (\n                      <div className='absolute inset-0 bg-red-500 bg-opacity-20 flex items-center justify-center'>\n                        <div className='bg-red-600 text-white px-3 py-1 rounded text-sm'>\n                          获取失败\n                        </div>\n                      </div>\n                    )}\n                    {video.success && video.videoUrl && (\n                      <div className='absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center'>\n                        <Play className='text-white opacity-0 hover:opacity-100 transition-opacity h-12 w-12' />\n                      </div>\n                    )}\n                  </div>\n                  <CardContent className='p-4'>\n                    <h3 className='font-semibold text-lg mb-2 line-clamp-2'>{video.name}</h3>\n                    {video.success ? (\n                      <div className='space-y-2 text-sm'>\n                        <div>\n                          <span className='text-gray-500'>原始URL:</span>\n                          <a\n                            className='ml-1 text-blue-600 hover:text-blue-800 inline-flex items-center'\n                            href={video.originalUrl}\n                            rel='noopener noreferrer'\n                            target='_blank'\n                          >\n                            查看原页面\n                            <ExternalLink className='ml-1 h-3 w-3' />\n                          </a>\n                        </div>\n                        {video.videoUrl && (\n                          <div>\n                            <span className='text-gray-500'>视频地址:</span>\n                            <a\n                              className='ml-1 text-blue-600 hover:text-blue-800 inline-flex items-center'\n                              href={video.videoUrl}\n                              rel='noopener noreferrer'\n                              target='_blank'\n                            >\n                              播放视频\n                              <Play className='ml-1 h-3 w-3' />\n                            </a>\n                          </div>\n                        )}\n                        <div className='pt-2'>\n                          <Button\n                            className='w-full bg-blue-600 hover:bg-blue-700 text-white'\n                            disabled={singleDownloading.has(video.id)}\n                            onClick={() => handleSingleDownload(video)}\n                            size='sm'\n                          >\n                            {singleDownloading.has(video.id) ? (\n                              <>\n                                <Loader2 className='mr-2 h-3 w-3 animate-spin' />\n                                下载中...\n                              </>\n                            ) : (\n                              <>\n                                <Download className='mr-2 h-3 w-3' />\n                                下载视频\n                              </>\n                            )}\n                          </Button>\n                        </div>\n                      </div>\n                    ) : (\n                      <div className='text-red-600 text-sm'>\n                        <span className='font-medium'>错误:</span> {video.error}\n                      </div>\n                    )}\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* 下载进度弹窗 */}\n        <DownloadProgressModal\n          onClose={() => {\n            if (downloader) {\n              downloader.clearProgress()\n            }\n          }}\n          progress={downloadProgress}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AApBA;;;;;;;;;;;;AAkDe,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAC/E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IACrE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAE5E,WAAW;IACX,MAAM,YAAY,CAAC;QACjB,MAAM,OAAO,MACV,KAAK,CAAC,SAAS,eAAe;SAC9B,GAAG,CAAC,CAAC,MAAQ,IAAI,IAAI,IACrB,MAAM,CAAC,CAAC,MAAQ,IAAI,MAAM,GAAG;QAChC,OAAO;eAAI,IAAI,IAAI;SAAM;IAC3B;IAEA,kBAAkB;IAClB,MAAM,eAAe,CAAC;QACpB,OAAO,UAAU;IACnB;IAEA,UAAU;IACV,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,mBAAmB;YACnB,MAAM,SAAS,kBAAkB,cAAc;;YAE/C,MAAM,WAAW,MAAM,MAAM,QAAQ;gBACnC,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAI;gBAC3B,SAAS;oBACP,gBAAgB;gBAClB;gBACA,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,SAAsB,MAAM,SAAS,IAAI;YAE/C,UAAU;YACV,IAAI,OAAO,IAAI,KAAK,QAAQ;gBAC1B,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;YACpC;YAEA,MAAM,UAAU,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACrD,OAAO;gBACL,OAAO,OAAO,IAAI,CAAC,KAAK;gBACxB,IAAI;gBACJ,MAAM,OAAO,IAAI,CAAC,IAAI,IAAI;gBAC1B,aAAa;gBACb,SAAS;gBACT,UAAU,OAAO,IAAI,CAAC,QAAQ;YAChC;QACF,EAAE,OAAO,OAAO;YACd,MAAM,UAAU,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACrD,OAAO;gBACL,OAAO;gBACP,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAChD,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,UAAU;YACZ;QACF;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,qBAAqB;YACrB,kBAAkB,wHAAA,CAAA,UAAO,CAAC,WAAW;YAErC,SAAS;YACT,MAAM,aAAa,wHAAA,CAAA,UAAO,CAAC,IAAI;YAC/B,IAAI,YAAY;gBACd,QAAQ,WAAW,IAAI;gBACvB,WAAW,WAAW,OAAO;gBAC7B,iBAAiB,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc;YAChE;YAEA,SAAS;YACT,MAAM,qBAAqB,IAAI,yHAAA,CAAA,kBAAe;+CAAC,CAAC;oBAC9C,oBAAoB;gBACtB;;YACA,cAAc;QAChB;sCAAG,EAAE;IAEL,YAAY;IACZ,MAAM,cAAc,CAAC,YAAoB;QACvC,IAAI,gBAAgB;YAClB,wHAAA,CAAA,UAAO,CAAC,IAAI,CAAC,YAAY;YACzB,iBAAiB,IAAI,OAAO,cAAc;QAC5C;IACF;IAEA,YAAY;IACZ,MAAM,aAAa;QACjB,wHAAA,CAAA,UAAO,CAAC,KAAK;QACb,QAAQ;QACR,WAAW,EAAE;QACb,iBAAiB;QACjB,SAAS;IACX;IAEA,MAAM,eAAe;QACnB,MAAM,UAAU,UAAU;QAE1B,IAAI,QAAQ,MAAM,KAAK,GAAG;YACxB,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QACT,WAAW,EAAE;QAEb,IAAI;YACF,eAAe;YACf,MAAM,WAAW,QAAQ,GAAG,CAAC,CAAC,MAAQ,eAAe;YACrD,MAAM,aAAa,MAAM,QAAQ,GAAG,CAAC;YAErC,aAAa;YACb,MAAM,eAAe,WAAW,MAAM,CAAC,CAAC,IAAM,EAAE,OAAO,EAAE,MAAM;YAC/D,MAAM,YAAY,WAAW,MAAM,GAAG;YAEtC,WAAW;YAEX,QAAQ;YACR,YAAY,MAAM;YAElB,IAAI,YAAY,GAAG;gBACjB,SAAS,GAAG,aAAa,KAAK,EAAE,UAAU,IAAI,CAAC;YACjD;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,WAAW;IACX,MAAM,sBAAsB;QAC1B,MAAM,mBAAmB,QAAQ,MAAM,CAAC,CAAC,QAAU,MAAM,OAAO,IAAI,MAAM,QAAQ;QAElF,IAAI,iBAAiB,MAAM,KAAK,GAAG;YACjC,SAAS;YACT;QACF;QAEA,IAAI,YAAY;YACd,MAAM,WAAW,cAAc,CAC7B,iBAAiB,GAAG,CAAC,CAAC,QAAU,CAAC;oBAC/B,IAAI,MAAM,EAAE;oBACZ,MAAM,MAAM,IAAI;oBAChB,UAAU,MAAM,QAAQ;gBAC1B,CAAC;QAEL;IACF;IAEA,SAAS;IACT,MAAM,uBAAuB,OAAO;QAClC,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,YAAY;QAEpC,qBAAqB,CAAC,OAAS,IAAI,IAAI,MAAM,GAAG,CAAC,MAAM,EAAE;QAEzD,IAAI;YACF,MAAM,WAAW,aAAa,CAAC,MAAM,QAAQ,EAAE,MAAM,IAAI,EAAE,MAAM,EAAE;QACrE,SAAU;YACR,qBAAqB,CAAC;gBACpB,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC,MAAM,EAAE;gBACtB,OAAO;YACT;QACF;IACF;IAEA,MAAM,gBAAgB,UAAU,MAAM,MAAM;IAC5C,MAAM,YAAY,aAAa;IAC/B,MAAM,mBAAmB,QAAQ,MAAM,CAAC,CAAC,QAAU,MAAM,OAAO,IAAI,MAAM,QAAQ;IAElF,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAG/B,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAEnB,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uIAAA,CAAA,WAAQ;4CACP,WAAU;4CACV,UAAU;4CACV,UAAU,CAAC;gDACT,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACtB,cAAc;gDACd,IAAI,gBAAgB;oDAClB,WAAW;wDACT,wHAAA,CAAA,UAAO,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE;oDAC/B,GAAG,MAAM,eAAe;;gDAC1B;4CACF;4CACA,aAAY;4CACZ,OAAO;;;;;;sDAET,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uIAAA,CAAA,aAAU;oDAAC,OAAO;oDAAe,MAAM,aAAa;;;;;;8DACrD,6LAAC;;wDAAM,KAAK,MAAM;wDAAC;;;;;;;;;;;;;;;;;;;gCAItB,gCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;;sEACd,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;gDAGvC,+BAAiB,6LAAC;;wDAAK;wDAAO;;;;;;;;;;;;;sDAEjC,6LAAC,qIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,SAAS;4CACT,MAAK;4CACL,SAAQ;;8DAER,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;gCAMxC,uBAAS,6LAAC;oCAAI,WAAU;8CAAiD;;;;;;8CAE1E,6LAAC,qIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,UAAU,WAAW,kBAAkB;oCACvC,SAAS;8CAER,wBACC;;0DACE,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD,CAAC,QAAQ,EAAE,cAAc,MAAM,CAAC;;;;;;;;;;;;;;;;;;8BAOxC,6LAAC,wIAAA,CAAA,cAAW;oBAAC,cAAc;oBAAY,SAAS;;;;;;gBAE/C,QAAQ,MAAM,GAAG,mBAChB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAuC;wCAC1C,QAAQ,MAAM;wCAAC;;;;;;;gCAEzB,iBAAiB,MAAM,GAAG,mBACzB,6LAAC,qIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,UAAU,iBAAiB,IAAI,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK;oCACpD,SAAS;;sDAET,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAiB;wCACnC,iBAAiB,MAAM;wCAAC;;;;;;;;;;;;;sCAIrC,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,sBACZ,6LAAC,mIAAA,CAAA,OAAI;oCACH,WAAW,CAAC,kDAAkD,EAAE,CAAC,MAAM,OAAO,GAAG,6BAA6B,IAAI;;sDAGlH,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,MAAM,IAAI;oDACf,WAAU;oDACV,QAAQ;oDACR,KAAK,MAAM,KAAK,IAAI;oDACpB,OAAO;;;;;;gDAER,CAAC,MAAM,OAAO,kBACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEAAkD;;;;;;;;;;;gDAKpE,MAAM,OAAO,IAAI,MAAM,QAAQ,kBAC9B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAItB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAG,WAAU;8DAA2C,MAAM,IAAI;;;;;;gDAClE,MAAM,OAAO,iBACZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEACC,WAAU;oEACV,MAAM,MAAM,WAAW;oEACvB,KAAI;oEACJ,QAAO;;wEACR;sFAEC,6LAAC,yNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;;;;;;;;wDAG3B,MAAM,QAAQ,kBACb,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6LAAC;oEACC,WAAU;oEACV,MAAM,MAAM,QAAQ;oEACpB,KAAI;oEACJ,QAAO;;wEACR;sFAEC,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;;;;;;;;sEAItB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEACL,WAAU;gEACV,UAAU,kBAAkB,GAAG,CAAC,MAAM,EAAE;gEACxC,SAAS,IAAM,qBAAqB;gEACpC,MAAK;0EAEJ,kBAAkB,GAAG,CAAC,MAAM,EAAE,kBAC7B;;sFACE,6LAAC,oNAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;wEAA8B;;iGAInD;;sFACE,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;yEAQ/C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAc;;;;;;wDAAU;wDAAE,MAAM,KAAK;;;;;;;;;;;;;;mCA5EtD,MAAM,EAAE;;;;;;;;;;;;;;;;8BAuFvB,6LAAC,6IAAA,CAAA,wBAAqB;oBACpB,SAAS;wBACP,IAAI,YAAY;4BACd,WAAW,aAAa;wBAC1B;oBACF;oBACA,UAAU;;;;;;;;;;;;;;;;;AAKpB;GAhYwB;KAAA", "debugId": null}}]}