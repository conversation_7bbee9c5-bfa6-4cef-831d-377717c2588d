{"$schema": "https://biomejs.dev/schemas/2.0.0/schema.json", "assist": {"actions": {"source": {"organizeImports": "on", "useSortedAttributes": "on", "useSortedKeys": "on", "useSortedProperties": "on"}}}, "files": {"ignoreUnknown": false, "includes": ["**"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100}, "javascript": {"formatter": {"jsxQuoteStyle": "single", "quoteStyle": "single", "semicolons": "asNeeded"}}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noInferrableTypes": "error", "noParameterAssign": "error", "noUnusedTemplateLiteral": "error", "noUselessElse": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useNumberNamespace": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error"}}}, "vcs": {"clientKind": "git", "enabled": false, "useIgnoreFile": false}}